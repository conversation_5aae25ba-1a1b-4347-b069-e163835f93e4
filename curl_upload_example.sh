#!/bin/bash

# curl example for process-documents-upload endpoint
# This script shows how to upload files to the API using curl

API_URL="http://localhost:8000"  # Change this to your server URL
ENDPOINT="$API_URL/process-documents-upload"

echo "🚀 Testing process-documents-upload endpoint with curl"
echo "=================================================="

# Create a test file
echo "Creating test file..."
cat > test_document.txt << 'EOF'
Bu bir test dokümandır.

Bu dosya curl ile yükleme testinde kullanılmaktadır.

İçerik:
- Test verisi 1
- Test verisi 2
- Test verisi 3

Bu dosya başarıyla yüklenip işlenirse, Chroma veritabanında saklanacaktır.
EOF

echo "Test file created: test_document.txt"

# Upload single file
echo ""
echo "1. Testing single file upload with curl:"
echo "Endpoint: $ENDPOINT"

curl -X POST "$ENDPOINT" \
  -F "files=@test_document.txt" \
  -F "collection_name=curl_test_collection" \
  -F "custom_metadata={\"source\":\"curl_upload\",\"test\":true}" \
  -F "overwrite_collection=false" \
  -H "Accept: application/json" \
  | python -m json.tool

echo ""
echo "2. Testing with multiple files:"

# Create additional test files
cat > test_document_2.txt << 'EOF'
İkinci test dokümandır.

Bu dosya çoklu yükleme testinde kullanılmaktadır.

Özellikler:
- Dosya numarası: 2
- Test türü: Çoklu yükleme
- Format: Metin
EOF

cat > test_document_3.txt << 'EOF'
Üçüncü test dokümandır.

Bu dosya da çoklu yükleme testinde kullanılmaktadır.

Bilgiler:
- Dosya numarası: 3
- Test türü: Çoklu yükleme
- Durum: Aktif
EOF

# Upload multiple files
curl -X POST "$ENDPOINT" \
  -F "files=@test_document.txt" \
  -F "files=@test_document_2.txt" \
  -F "files=@test_document_3.txt" \
  -F "collection_name=curl_multi_test_collection" \
  -F "custom_metadata={\"source\":\"curl_multi_upload\",\"test\":true,\"batch\":true}" \
  -F "overwrite_collection=false" \
  -H "Accept: application/json" \
  | python -m json.tool

# Clean up test files
echo ""
echo "Cleaning up test files..."
rm -f test_document.txt test_document_2.txt test_document_3.txt
echo "✅ Test files cleaned up"

echo ""
echo "✅ curl test completed!"
echo ""
echo "Usage examples:"
echo "==============="
echo ""
echo "Single file upload:"
echo "curl -X POST \"$ENDPOINT\" \\"
echo "  -F \"files=@your_document.pdf\" \\"
echo "  -F \"collection_name=my_collection\" \\"
echo "  -F \"custom_metadata={\\\"source\\\":\\\"external\\\"}\" \\"
echo "  -F \"overwrite_collection=false\""
echo ""
echo "Multiple files upload:"
echo "curl -X POST \"$ENDPOINT\" \\"
echo "  -F \"files=@document1.pdf\" \\"
echo "  -F \"files=@document2.docx\" \\"
echo "  -F \"files=@document3.txt\" \\"
echo "  -F \"collection_name=my_collection\" \\"
echo "  -F \"custom_metadata={\\\"source\\\":\\\"batch_upload\\\"}\""
echo ""
echo "Supported file types: .pdf, .docx, .doc, .txt, .md"
