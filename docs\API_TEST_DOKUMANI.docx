Agentic RAG API Test Dökümanı

GENEL BİLGİLER

Test Ortamları:
• Atlas Üniversitesi Ağı: http://10.1.1.172:3820/docs
• Netax Ortamı: http://212.174.1.206:3820/docs
• API Türü: FastAPI (REST)
• Content-Type: application/json

API ENDPOİNTLERİ

1. Sistem Durumu Kontrolü
   Endpoint: GET /
   Amaç: Sistem sağlık kontrolü
   Beklenen Yanıt: {"status": "ok", "message": "Agentic RAG API is running"}

2. Bot Listesi
   Endpoint: GET /bots
   Amaç: Mevcut tüm botları listele
   Yanıt: Bot isimleri, açıklamaları ve araçları

3. Bot Bilgisi
   Endpoint: GET /bots/{bot_name}
   Amaç: Belirli bir bot hakkında detay bilgi
   Parametre: bot_name (URL'de)

4. <PERSON><PERSON>rgusu (Ana Özellik)
   Endpoint: POST /bots/{bot_name}/query
   Amaç: Bota soru sorma
   Request Body:
   {
     "query": "Soru metni",
     "session_id": "oturum_id (opsiyonel)",
     "metadata": {}
   }

5. Hafıza Temizleme
   Endpoint: DELETE /bots/{bot_name}/memory
   Amaç: Bot oturum hafızasını temizle
   Query Parameter: session_id (zorunlu)

6. Döküman İşleme
   Endpoint: POST /process-documents
   Amaç: Klasördeki dökümanları işleyip veritabanına kaydet
   Request Body:
   {
     "directory_path": "/path/to/documents",
     "collection_name": "koleksiyon_adi",
     "recursive": true,
     "custom_metadata": {}
   }

7. Döküman Yükleme (YENİ)
   Endpoint: POST /process-documents-upload
   Amaç: Dışarıdan dosya yükleyip işleme
   Content-Type: multipart/form-data
   Form Data:
   - files: Yüklenecek dosyalar (çoklu dosya desteklenir)
   - collection_name: Koleksiyon adı (zorunlu)
   - custom_metadata: JSON formatında ek metadata (opsiyonel)
   - overwrite_collection: Mevcut koleksiyonun üzerine yazma (opsiyonel, varsayılan: false)

   Desteklenen formatlar: .pdf, .docx, .doc, .txt, .md

TEST SENARYOLARI

Temel Testler:
1. Sistem Durumu: GET / → 200 OK
2. Bot Listesi: GET /bots → Bot listesi dönmeli
3. Geçersiz Bot: GET /bots/yokbot → 404 Not Found

Sorgu Testleri:
1. Basit Sorgu: POST /bots/general/query Body: {"query": "Merhaba"}
2. Oturum ile Sorgu: POST /bots/general/query Body: {"query": "Önceki soruyu hatırlıyor musun?", "session_id": "test123"}
3. Uzun Sorgu: 1000+ karakter sorgu gönder

Hata Testleri:
1. Boş Sorgu: {"query": ""} → Hata dönmeli
2. Geçersiz JSON: Bozuk JSON gönder
3. Eksik Parametre: query alanı olmadan gönder

Döküman İşleme Testleri:
1. Geçerli Klasör: Var olan klasör yolu
2. Geçersiz Klasör: Olmayan klasör yolu → Hata
3. Boş Klasör: İçi boş klasör

BEKLENEN YANITLAR

Başarılı Sorgu Yanıtı:
{
  "bot_name": "general",
  "query": "Soru",
  "response": "Bot yanıtı",
  "tool_responses": [...],
  "session_id": "oturum_id"
}

Hata Yanıtı:
{
  "error": "Hata mesajı",
  "details": {...}
}

CURL KOMUTLARI

Atlas Üniversitesi Ağı (10.1.1.172:3820):

# Sistem durumu
curl -X GET http://10.1.1.172:3820/

# Bot listesi
curl -X GET http://10.1.1.172:3820/bots

# Bot bilgisi
curl -X GET http://10.1.1.172:3820/bots/general

# Bot sorgusu
curl -X POST http://10.1.1.172:3820/bots/general/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Test sorusu"}'

# Oturum ile sorgu
curl -X POST http://10.1.1.172:3820/bots/general/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Merhaba", "session_id": "test123"}'

# Hafıza temizleme
curl -X DELETE "http://10.1.1.172:3820/bots/general/memory?session_id=test123"

# Döküman işleme
curl -X POST http://10.1.1.172:3820/process-documents \
  -H "Content-Type: application/json" \
  -d '{"directory_path": "/test/docs", "collection_name": "test_collection"}'

# Döküman yükleme (YENİ)
curl -X POST http://10.1.1.172:3820/process-documents-upload \
  -F "files=@document.pdf" \
  -F "collection_name=upload_test" \
  -F "custom_metadata={\"source\":\"external\"}"

# Çoklu dosya yükleme
curl -X POST http://10.1.1.172:3820/process-documents-upload \
  -F "files=@doc1.pdf" \
  -F "files=@doc2.txt" \
  -F "files=@doc3.docx" \
  -F "collection_name=multi_upload_test"

Netax Ortamı (212.174.1.206:3820):

# Sistem durumu
curl -X GET http://212.174.1.206:3820/

# Bot listesi
curl -X GET http://212.174.1.206:3820/bots

# Bot bilgisi
curl -X GET http://212.174.1.206:3820/bots/general

# Bot sorgusu
curl -X POST http://212.174.1.206:3820/bots/general/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Test sorusu"}'

# Oturum ile sorgu
curl -X POST http://212.174.1.206:3820/bots/general/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Merhaba", "session_id": "test123"}'

# Hafıza temizleme
curl -X DELETE "http://212.174.1.206:3820/bots/general/memory?session_id=test123"

# Döküman işleme
curl -X POST http://212.174.1.206:3820/process-documents \
  -H "Content-Type: application/json" \
  -d '{"directory_path": "/test/docs", "collection_name": "test_collection"}'

SQL VERİTABANI SORGUSU TOOL TESTLERİ

SQL Tool Özellikleri:
• Doğal dil sorgularını SQL'e çevirir
• MSSQL Server bağlantısı (VERSISDB)
• Maksimum 50 sonuç döner

Test Sorguları:
1. "Veritabanında birimlist tablosuna göre kaç tane personel var?"
2. "Veritabanına göre bilgisayar mühendisliği bölümündeki öğretim üyeleri"
3. "Veritabanına göre 2024 yılı bütçe bilgileri"

Beklenen SQL Tool Yanıtı:
{
  "success": true,
  "query": "SELECT COUNT(*) FROM staff",
  "results": [...],
  "count": 1,
  "columns": ["count"],
  "llm_used": true
}

WEB ARAMA TOOL TESTLERİ

Web Search Tool Özellikleri:
• Tavily API kullanır
• Maksimum 5 sonuç döner
• Eğitim sitelerini (.edu, .org) öncelikler
• Sosyal medya sitelerini (.facebook, .twitter) hariç tutar

Test Sorguları:
1. "Youtubedan python kursu öner"
2. "Makine öğrenmesi ile ilgili makale getir"
3. "2024 üniversite sıralamaları"

Beklenen Web Search Yanıtı:
{
  "success": true,
  "query": "2024 üniversite sıralamaları",
  "results": [
    {
      "title": "Makine öğrenmesi ile ilgili makale getir",
      "url": "https://example.edu/ml-education",
      "content": "...",
      "score": 0.95
    }
  ],
  "count": 5
}

TOOL TESTİ İÇİN CURL KOMUTLARI

Atlas Üniversitesi Ağı - SQL Sorgusu:
curl -X POST http://10.1.1.172:3820/bots/admin/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Veritabanında kaç tane personel var?"}'

curl -X POST http://10.1.1.172:3820/bots/student/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Veritabanında bilgisayar mühendisliği bölümündeki öğretim üyeleri"}'

Atlas Üniversitesi Ağı - Web Araması:
curl -X POST http://10.1.1.172:3820/bots/academic/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Makine öğrenmesi ile ilgili makale getir"}'

curl -X POST http://10.1.1.172:3820/bots/atlasiq/query \
  -H "Content-Type: application/json" \
  -d '{"query": "machine learning trends 2024"}'

Netax Ortamı - SQL Sorgusu:
curl -X POST http://212.174.1.206:3820/bots/admin/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Veritabanına göre kaç tane personel var?"}'

curl -X POST http://212.174.1.206:3820/bots/student/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Veritabanına göre bilgisayar mühendisliği bölümündeki öğretim üyeleri"}'

Netax Ortamı - Web Araması:
curl -X POST http://212.174.1.206:3820/bots/academic/query \
  -H "Content-Type: application/json" \
  -d '{"query": "artificial intelligence research papers"}'

curl -X POST http://212.174.1.206:3820/bots/atlasiq/query \
  -H "Content-Type: application/json" \
  -d '{"query": "machine learning trends 2024"}'

TOOL TEST SENARYOLARI

SQL Tool Test Senaryoları:
1. Basit Sayma Sorgusu: "Veritabanına göre kaç personel var?" → COUNT sorgusu
2. Filtreleme Sorgusu: "Mühendislik fakültesindeki personeller" → WHERE koşulu
3. Geçersiz Tablo: "students tablosundan veri" → Hata (izin yok)
4. Karmaşık Sorgu: "Departman bazında personel sayısı" → GROUP BY

Web Search Tool Test Senaryoları:
1. Akademik Sorgu: "machine learning research" → .edu siteleri
2. Genel Sorgu: "artificial intelligence" → Çeşitli kaynaklar
3. Türkçe Sorgu: "yapay zeka eğitim" → Türkçe sonuçlar
4. Sosyal Medya Filtresi: "AI facebook" → Facebook hariç tutulmalı
5. Uzun Sorgu: 200+ karakter sorgu

KRİTİK TEST NOKTALARI

✓ Türkçe karakter desteği
✓ Uzun sorgu işleme
✓ Oturum yönetimi
✓ Hata durumları
✓ Yanıt süreleri
✓ Eş zamanlı istekler
✓ SQL injection koruması
✓ Web arama filtreleme
✓ Tool seçim mantığı

TEST ARAÇLARI

• Swagger UI: Her iki ortamda /docs endpoint'i
• Postman/Insomnia: GUI test için
• curl: Komut satırı test için

NOT: Swagger UI üzerinden interaktif test yapabilirsiniz. Tüm endpoint'ler otomatik dokümante edilmiştir. Bot'lar farklı tool'lara sahiptir.