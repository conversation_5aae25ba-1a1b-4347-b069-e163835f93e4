# Document Upload Endpoint

Bu do<PERSON>üman<PERSON>yon, dı<PERSON><PERSON><PERSON>dan dosya ve klasör yükleme için o<PERSON>ula<PERSON> `process-documents-upload` endpoint'ini açıklar.

## Endpoint Bilgileri

- **URL**: `/process-documents-upload`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Tags**: `Documents`

## Özellikler

✅ **Tek dosya yükleme**: Bireysel dosyaları yükleyebilirsiniz  
✅ **Çoklu dosya yükleme**: Aynı anda birden fazla dosya yükleyebilirsiniz  
✅ **Klasör yükleme**: Klasörleri ZIP dosyası olarak yükleyebilirsiniz  
✅ **Recursive işleme**: Alt klasörleri de otomatik olarak işler  
✅ **Otomatik ZIP çıkarma**: ZIP dosyaları otomatik olarak çıkarılır ve işlenir  

## Parametreler

### Form Data Parametreleri

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `files` | `List[UploadFile]` | ✅ | Yüklenecek dosyalar veya ZIP klasörleri |
| `collection_name` | `string` | ✅ | Dökümanların saklanacağı koleksiyon adı |
| `custom_metadata` | `string` | ❌ | JSON formatında ek metadata |
| `overwrite_collection` | `boolean` | ❌ | Mevcut koleksiyonun üzerine yazılıp yazılmayacağı (varsayılan: false) |
| `recursive` | `boolean` | ❌ | Alt klasörlerin işlenip işlenmeyeceği (varsayılan: true) |

### Desteklenen Dosya Formatları

- `.pdf` - PDF dökümanları
- `.docx` - Microsoft Word dökümanları (yeni format)
- `.doc` - Microsoft Word dökümanları (eski format)
- `.txt` - Metin dosyaları
- `.md` - Markdown dosyaları
- `.zip` - Klasör yapısı (otomatik olarak çıkarılır)

## Klasör Yükleme Nasıl Çalışır?

1. **Klasörünüzü ZIP dosyası yapın**:
   ```bash
   zip -r my_documents.zip my_documents/
   ```

2. **ZIP dosyasını yükleyin**:
   - API, `.zip` uzantılı dosyaları otomatik olarak algılar
   - ZIP dosyası geçici bir klasöre çıkarılır
   - Çıkarılan klasör yapısı recursive olarak işlenir
   - Tüm desteklenen dosya türleri otomatik olarak bulunur ve işlenir

3. **Sonuç**:
   - Ana klasör ve tüm alt klasörlerdeki dosyalar işlenir
   - Her dosya ayrı ayrı vektör veritabanına eklenir
   - Klasör yapısı metadata olarak korunur

## Yanıt Formatı

```json
{
  "success": true,
  "message": "Successfully processed 5 files from uploaded directory",
  "directory_path": "uploaded_files",
  "collection_name": "my_collection",
  "total_files": 5,
  "successful_count": 5,
  "failed_count": 0,
  "details": {
    "upload_source": "external",
    "upload_method": "directory_and_files",
    "processed_files": [
      {
        "file_path": "document1.pdf",
        "success": true,
        "chunk_count": 10
      },
      {
        "file_path": "subfolder/document2.txt",
        "success": true,
        "chunk_count": 3
      }
    ]
  }
}
```

## Kullanım Örnekleri

### 1. Tek Dosya Yükleme

```python
import requests

files = [('files', ('document.pdf', open('document.pdf', 'rb'), 'application/pdf'))]
data = {
    'collection_name': 'my_documents',
    'custom_metadata': '{"source": "external"}',
    'recursive': True
}

response = requests.post('http://your-server:8000/process-documents-upload', 
                        files=files, data=data)
```

### 2. Çoklu Dosya Yükleme

```python
files = [
    ('files', ('doc1.pdf', open('doc1.pdf', 'rb'), 'application/pdf')),
    ('files', ('doc2.txt', open('doc2.txt', 'rb'), 'text/plain')),
    ('files', ('doc3.docx', open('doc3.docx', 'rb'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'))
]

response = requests.post('http://your-server:8000/process-documents-upload', 
                        files=files, data=data)
```

### 3. Klasör Yükleme (ZIP)

```python
# Önce klasörü zip yapın
import zipfile
with zipfile.ZipFile('my_documents.zip', 'w') as zipf:
    for root, dirs, files in os.walk('my_documents/'):
        for file in files:
            zipf.write(os.path.join(root, file), 
                      os.path.relpath(os.path.join(root, file), 'my_documents/'))

# Sonra zip'i yükleyin
files = [('files', ('my_documents.zip', open('my_documents.zip', 'rb'), 'application/zip'))]
data = {
    'collection_name': 'my_collection',
    'recursive': True  # Alt klasörleri de işle
}

response = requests.post('http://your-server:8000/process-documents-upload', 
                        files=files, data=data)
```

## cURL Örnekleri

### Tek dosya:
```bash
curl -X POST "http://your-server:8000/process-documents-upload" \
  -F "files=@document.pdf" \
  -F "collection_name=my_collection" \
  -F "recursive=true"
```

### Çoklu dosya:
```bash
curl -X POST "http://your-server:8000/process-documents-upload" \
  -F "files=@doc1.pdf" \
  -F "files=@doc2.txt" \
  -F "files=@doc3.docx" \
  -F "collection_name=my_collection" \
  -F "recursive=true"
```

### Klasör (ZIP):
```bash
# Önce klasörü zip yapın
zip -r my_documents.zip my_documents/

# Sonra yükleyin
curl -X POST "http://your-server:8000/process-documents-upload" \
  -F "files=@my_documents.zip" \
  -F "collection_name=my_collection" \
  -F "recursive=true"
```

## JavaScript/Web Örneği

```html
<input type="file" id="fileInput" multiple webkitdirectory>
<button onclick="uploadFiles()">Upload Directory</button>

<script>
async function uploadFiles() {
    const fileInput = document.getElementById('fileInput');
    const files = fileInput.files;
    
    // Klasör yapısını ZIP olarak oluştur (client-side zip library gerekli)
    const zip = new JSZip();
    
    for (let file of files) {
        zip.file(file.webkitRelativePath, file);
    }
    
    const zipBlob = await zip.generateAsync({type: "blob"});
    
    const formData = new FormData();
    formData.append('files', zipBlob, 'uploaded_directory.zip');
    formData.append('collection_name', 'web_upload');
    formData.append('recursive', true);
    
    const response = await fetch('/process-documents-upload', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    console.log(result);
}
</script>
```

## Docker Ortamında Kullanım

```bash
# Container'ı başlat
docker run -d -p 8000:8000 --name atlas-rag your-image-name

# Klasör yükle
zip -r documents.zip documents/
curl -X POST "http://your-ec2-ip:8000/process-documents-upload" \
  -F "files=@documents.zip" \
  -F "collection_name=production_docs" \
  -F "recursive=true"
```

## Güvenlik ve Performans

- **Dosya boyutu limiti**: Büyük ZIP dosyaları için uygun timeout ayarlayın
- **ZIP bomb koruması**: Çok büyük sıkıştırılmış dosyalara dikkat edin
- **Geçici dosya temizliği**: Tüm geçici dosyalar otomatik olarak temizlenir
- **Recursive limit**: Çok derin klasör yapılarında performans düşebilir

## Hata Durumları

- **Geçersiz ZIP**: ZIP olmayan dosyalar normal dosya olarak işlenir
- **Boş klasör**: Desteklenen dosya bulunamazsa hata döner
- **Büyük dosyalar**: Timeout hatası alabilirsiniz
- **Desteklenmeyen format**: Sadece belirtilen formatlar işlenir
