#!/usr/bin/env python3
"""
Test script for the process-documents-upload endpoint.
This script demonstrates how to upload files to the API from external sources.
"""

import requests
import json
import os
from pathlib import Path

# API configuration
API_BASE_URL = "http://localhost:8001"  # Change this to your server URL
UPLOAD_ENDPOINT = f"{API_BASE_URL}/process-documents-upload"


def create_test_file():
    """Create a test document for upload."""
    test_content = """
    Bu bir test dokümandır.
    
    Bu dosya, process-documents-upload endpoint'ini test etmek için oluşturulmuştur.
    
    İçerik:
    - Test verisi 1
    - Test verisi 2  
    - Test verisi 3
    
    Bu dosya başarıyla yüklenip işlenirse, Chroma veritabanında saklanacaktır.
    """

    test_file_path = Path("test_upload_document.txt")
    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write(test_content)

    return test_file_path


def upload_documents():
    """Upload documents to the API."""
    try:
        # Create test file
        test_file = create_test_file()

        # Prepare files for upload
        files = [("files", (test_file.name, open(test_file, "rb"), "text/plain"))]

        # Prepare form data
        data = {
            "collection_name": "test_upload_collection",
            "custom_metadata": json.dumps(
                {
                    "source": "external_upload",
                    "test": True,
                    "uploaded_by": "test_script",
                }
            ),
            "overwrite_collection": False,
        }

        print(f"Uploading file: {test_file.name}")
        print(f"Collection: {data['collection_name']}")
        print(f"API URL: {UPLOAD_ENDPOINT}")

        # Make the request
        response = requests.post(UPLOAD_ENDPOINT, files=files, data=data)

        # Close file handle
        files[0][1][1].close()

        # Check response
        if response.status_code == 200:
            result = response.json()
            print("\n✅ Upload successful!")
            print(f"Message: {result['message']}")
            print(f"Total files: {result['total_files']}")
            print(f"Successful: {result['successful_count']}")
            print(f"Failed: {result['failed_count']}")

            if result.get("details"):
                print("\nDetails:")
                for file_result in result["details"].get("results", []):
                    status = "✅" if file_result["success"] else "❌"
                    print(
                        f"  {status} {file_result['filename']} - Chunks: {file_result.get('chunk_count', 0)}"
                    )
                    if file_result.get("error"):
                        print(f"    Error: {file_result['error']}")
        else:
            print(f"\n❌ Upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")

        # Clean up test file
        if test_file.exists():
            test_file.unlink()
            print(f"\nCleaned up test file: {test_file.name}")

    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to API at {API_BASE_URL}")
        print("Make sure the server is running!")
    except Exception as e:
        print(f"❌ Error during upload: {str(e)}")


def test_with_multiple_files():
    """Test uploading multiple files at once."""
    try:
        # Create multiple test files
        test_files = []
        for i in range(3):
            content = f"""
            Test Document {i+1}

            Bu {i+1}. test dokümandır.

            İçerik:
            - Dosya numarası: {i+1}
            - Test verisi: {i*10}
            - Rastgele bilgi: Bu dosya çoklu yükleme testinde kullanılıyor.
            """

            file_path = Path(f"test_upload_doc_{i+1}.txt")
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            test_files.append(file_path)

        # Prepare files for upload
        files = []
        for test_file in test_files:
            files.append(
                ("files", (test_file.name, open(test_file, "rb"), "text/plain"))
            )

        # Prepare form data
        data = {
            "collection_name": "test_multi_upload_collection",
            "custom_metadata": json.dumps(
                {"source": "multi_file_upload", "test": True, "batch_upload": True}
            ),
            "overwrite_collection": False,
            "recursive": True,
        }

        print(f"\nUploading {len(test_files)} files...")
        print(f"Collection: {data['collection_name']}")

        # Make the request
        response = requests.post(UPLOAD_ENDPOINT, files=files, data=data)

        # Close file handles
        for file_tuple in files:
            file_tuple[1][1].close()

        # Check response
        if response.status_code == 200:
            result = response.json()
            print("\n✅ Multi-file upload successful!")
            print(f"Message: {result['message']}")
            print(f"Total files: {result['total_files']}")
            print(f"Successful: {result['successful_count']}")
            print(f"Failed: {result['failed_count']}")
        else:
            print(
                f"\n❌ Multi-file upload failed with status code: {response.status_code}"
            )
            print(f"Response: {response.text}")

        # Clean up test files
        for test_file in test_files:
            if test_file.exists():
                test_file.unlink()
        print(f"\nCleaned up {len(test_files)} test files")

    except Exception as e:
        print(f"❌ Error during multi-file upload: {str(e)}")


def test_directory_upload():
    """Test uploading a directory as a zip file."""
    try:
        import zipfile
        import tempfile

        # Create a temporary directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create test directory structure
            test_dir = temp_path / "test_documents"
            test_dir.mkdir()

            # Create subdirectory
            sub_dir = test_dir / "subdirectory"
            sub_dir.mkdir()

            # Create files in main directory
            (test_dir / "main_doc1.txt").write_text(
                """
            Ana Klasör Dökümanı 1

            Bu dosya ana klasörde bulunmaktadır.

            İçerik:
            - Ana klasör dosyası
            - Test verisi: Ana-1
            - Kategori: Genel
            """,
                encoding="utf-8",
            )

            (test_dir / "main_doc2.md").write_text(
                """
            # Ana Klasör Dökümanı 2

            Bu bir **Markdown** dosyasıdır.

            ## Özellikler
            - Markdown formatı
            - Ana klasörde
            - Test amaçlı
            """,
                encoding="utf-8",
            )

            # Create files in subdirectory
            (sub_dir / "sub_doc1.txt").write_text(
                """
            Alt Klasör Dökümanı 1

            Bu dosya alt klasörde bulunmaktadır.

            İçerik:
            - Alt klasör dosyası
            - Test verisi: Alt-1
            - Kategori: Alt klasör
            """,
                encoding="utf-8",
            )

            (sub_dir / "sub_doc2.txt").write_text(
                """
            Alt Klasör Dökümanı 2

            Bu da alt klasörde bulunmaktadır.

            İçerik:
            - Alt klasör dosyası
            - Test verisi: Alt-2
            - Kategori: Alt klasör
            """,
                encoding="utf-8",
            )

            # Create zip file
            zip_path = Path("test_directory.zip")
            with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for file_path in test_dir.rglob("*"):
                    if file_path.is_file():
                        # Add file to zip with relative path
                        arcname = file_path.relative_to(test_dir)
                        zipf.write(file_path, arcname)

            print(f"\nCreated test directory zip: {zip_path.name}")

            # Upload the zip file
            files = [
                ("files", (zip_path.name, open(zip_path, "rb"), "application/zip"))
            ]

            data = {
                "collection_name": "test_directory_upload_collection",
                "custom_metadata": json.dumps(
                    {
                        "source": "directory_upload",
                        "test": True,
                        "upload_type": "zip_directory",
                    }
                ),
                "overwrite_collection": False,
                "recursive": True,
            }

            print(f"Uploading directory as zip...")
            print(f"Collection: {data['collection_name']}")
            print(f"Recursive: {data['recursive']}")

            # Make the request
            response = requests.post(UPLOAD_ENDPOINT, files=files, data=data)

            # Close file handle
            files[0][1][1].close()

            # Check response
            if response.status_code == 200:
                result = response.json()
                print("\n✅ Directory upload successful!")
                print(f"Message: {result['message']}")
                print(f"Total files: {result['total_files']}")
                print(f"Successful: {result['successful_count']}")
                print(f"Failed: {result['failed_count']}")

                if result.get("details"):
                    print(
                        f"Upload method: {result['details'].get('upload_method', 'N/A')}"
                    )
                    print(
                        f"Upload source: {result['details'].get('upload_source', 'N/A')}"
                    )
            else:
                print(
                    f"\n❌ Directory upload failed with status code: {response.status_code}"
                )
                print(f"Response: {response.text}")

            # Clean up zip file
            if zip_path.exists():
                zip_path.unlink()
                print(f"\nCleaned up zip file: {zip_path.name}")

    except Exception as e:
        print(f"❌ Error during directory upload: {str(e)}")


if __name__ == "__main__":
    print("🚀 Testing process-documents-upload endpoint")
    print("=" * 50)

    # Test single file upload
    print("\n1. Testing single file upload:")
    upload_documents()

    # Test multiple file upload
    print("\n2. Testing multiple file upload:")
    test_with_multiple_files()

    # Test directory upload
    print("\n3. Testing directory upload (zip):")
    test_directory_upload()

    print("\n✅ All tests completed!")
