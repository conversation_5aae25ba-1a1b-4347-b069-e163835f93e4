#!/usr/bin/env python3
"""
Test script for the process-documents-upload endpoint.
This script demonstrates how to upload files to the API from external sources.
"""

import requests
import json
import os
from pathlib import Path

# API configuration
API_BASE_URL = "http://localhost:8000"  # Change this to your server URL
UPLOAD_ENDPOINT = f"{API_BASE_URL}/process-documents-upload"

def create_test_file():
    """Create a test document for upload."""
    test_content = """
    Bu bir test dokümandır.
    
    Bu dosya, process-documents-upload endpoint'ini test etmek için oluşturulmuştur.
    
    İçerik:
    - Test verisi 1
    - Test verisi 2  
    - Test verisi 3
    
    Bu dosya başarıyla yüklenip işlenirse, Chroma veritabanında saklanacaktır.
    """
    
    test_file_path = Path("test_upload_document.txt")
    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write(test_content)
    
    return test_file_path

def upload_documents():
    """Upload documents to the API."""
    try:
        # Create test file
        test_file = create_test_file()
        
        # Prepare files for upload
        files = [
            ('files', (test_file.name, open(test_file, 'rb'), 'text/plain'))
        ]
        
        # Prepare form data
        data = {
            'collection_name': 'test_upload_collection',
            'custom_metadata': json.dumps({
                'source': 'external_upload',
                'test': True,
                'uploaded_by': 'test_script'
            }),
            'overwrite_collection': False
        }
        
        print(f"Uploading file: {test_file.name}")
        print(f"Collection: {data['collection_name']}")
        print(f"API URL: {UPLOAD_ENDPOINT}")
        
        # Make the request
        response = requests.post(UPLOAD_ENDPOINT, files=files, data=data)
        
        # Close file handle
        files[0][1][1].close()
        
        # Check response
        if response.status_code == 200:
            result = response.json()
            print("\n✅ Upload successful!")
            print(f"Message: {result['message']}")
            print(f"Total files: {result['total_files']}")
            print(f"Successful: {result['successful_count']}")
            print(f"Failed: {result['failed_count']}")
            
            if result.get('details'):
                print("\nDetails:")
                for file_result in result['details'].get('results', []):
                    status = "✅" if file_result['success'] else "❌"
                    print(f"  {status} {file_result['filename']} - Chunks: {file_result.get('chunk_count', 0)}")
                    if file_result.get('error'):
                        print(f"    Error: {file_result['error']}")
        else:
            print(f"\n❌ Upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Clean up test file
        if test_file.exists():
            test_file.unlink()
            print(f"\nCleaned up test file: {test_file.name}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to API at {API_BASE_URL}")
        print("Make sure the server is running!")
    except Exception as e:
        print(f"❌ Error during upload: {str(e)}")

def test_with_multiple_files():
    """Test uploading multiple files at once."""
    try:
        # Create multiple test files
        test_files = []
        for i in range(3):
            content = f"""
            Test Document {i+1}
            
            Bu {i+1}. test dokümandır.
            
            İçerik:
            - Dosya numarası: {i+1}
            - Test verisi: {i*10}
            - Rastgele bilgi: Bu dosya çoklu yükleme testinde kullanılıyor.
            """
            
            file_path = Path(f"test_upload_doc_{i+1}.txt")
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            test_files.append(file_path)
        
        # Prepare files for upload
        files = []
        for test_file in test_files:
            files.append(('files', (test_file.name, open(test_file, 'rb'), 'text/plain')))
        
        # Prepare form data
        data = {
            'collection_name': 'test_multi_upload_collection',
            'custom_metadata': json.dumps({
                'source': 'multi_file_upload',
                'test': True,
                'batch_upload': True
            }),
            'overwrite_collection': False
        }
        
        print(f"\nUploading {len(test_files)} files...")
        print(f"Collection: {data['collection_name']}")
        
        # Make the request
        response = requests.post(UPLOAD_ENDPOINT, files=files, data=data)
        
        # Close file handles
        for file_tuple in files:
            file_tuple[1][1].close()
        
        # Check response
        if response.status_code == 200:
            result = response.json()
            print("\n✅ Multi-file upload successful!")
            print(f"Message: {result['message']}")
            print(f"Total files: {result['total_files']}")
            print(f"Successful: {result['successful_count']}")
            print(f"Failed: {result['failed_count']}")
        else:
            print(f"\n❌ Multi-file upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Clean up test files
        for test_file in test_files:
            if test_file.exists():
                test_file.unlink()
        print(f"\nCleaned up {len(test_files)} test files")
            
    except Exception as e:
        print(f"❌ Error during multi-file upload: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing process-documents-upload endpoint")
    print("=" * 50)
    
    # Test single file upload
    print("\n1. Testing single file upload:")
    upload_documents()
    
    # Test multiple file upload
    print("\n2. Testing multiple file upload:")
    test_with_multiple_files()
    
    print("\n✅ Test completed!")
