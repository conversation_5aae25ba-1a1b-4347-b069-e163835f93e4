{"data\\all_docs\\sample_document.txt": {"file_name": "sample_document.txt", "file_size": 464, "processed_at": "2025-07-29T14:26:58.711178", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 1, "status": "success"}, "data\\all_docs\\myform\\01_user_authentication___management_.md": {"file_name": "01_user_authentication___management_.md", "file_size": 40501, "processed_at": "2025-07-29T14:27:03.822319", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 45, "status": "success"}, "data\\all_docs\\myform\\02_form_structure___management_.md": {"file_name": "02_form_structure___management_.md", "file_size": 27932, "processed_at": "2025-07-29T14:27:04.847367", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 9, "status": "success"}, "data\\all_docs\\myform\\03_form_responses___analytics_.md": {"file_name": "03_form_responses___analytics_.md", "file_size": 28567, "processed_at": "2025-07-29T14:27:06.379009", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 30, "status": "success"}, "data\\all_docs\\myform\\04_ai_integration_.md": {"file_name": "04_ai_integration_.md", "file_size": 34848, "processed_at": "2025-07-29T14:27:07.386816", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 6, "status": "success"}, "data\\all_docs\\myform\\05_next_js_api_routes_.md": {"file_name": "05_next_js_api_routes_.md", "file_size": 20696, "processed_at": "2025-07-29T14:27:09.188875", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 24, "status": "success"}, "data\\all_docs\\myform\\06_database_connection___models_.md": {"file_name": "06_database_connection___models_.md", "file_size": 20388, "processed_at": "2025-07-29T14:27:10.657347", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 23, "status": "success"}, "data\\all_docs\\myform\\07_global_middleware_.md": {"file_name": "07_global_middleware_.md", "file_size": 13431, "processed_at": "2025-07-29T14:27:11.786002", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 16, "status": "success"}, "data\\all_docs\\myform\\08_localization__i18n__.md": {"file_name": "08_localization__i18n__.md", "file_size": 20694, "processed_at": "2025-07-29T14:27:13.220839", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 25, "status": "success"}, "data\\all_docs\\myform\\index.md": {"file_name": "index.md", "file_size": 1819, "processed_at": "2025-07-29T14:27:13.852208", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 2, "status": "success"}, "data\\all_docs\\mytask\\01_user_and_authentication_system_.md": {"file_name": "01_user_and_authentication_system_.md", "file_size": 25793, "processed_at": "2025-07-29T14:27:16.291300", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 29, "status": "success"}, "data\\all_docs\\mytask\\02_task_management_core_.md": {"file_name": "02_task_management_core_.md", "file_size": 20862, "processed_at": "2025-07-29T14:27:17.080221", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 3, "status": "success"}, "data\\all_docs\\mytask\\03_frontend_state_management__redux_toolkit__.md": {"file_name": "03_frontend_state_management__redux_toolkit__.md", "file_size": 28618, "processed_at": "2025-07-29T14:27:18.808272", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 33, "status": "success"}, "data\\all_docs\\mytask\\04_api_communication__rtk_query__.md": {"file_name": "04_api_communication__rtk_query__.md", "processed_at": "2025-07-29T14:27:19.981513", "status": "error", "error": "[WinError 3] Sistem belirtilen yolu bulamıyor: 'data\\\\all_docs\\\\mytask\\\\04_api_communication__rtk_query__.md'"}, "data\\all_docs\\mytask\\05_api_endpoints_and_controllers__backend__.md": {"file_name": "05_api_endpoints_and_controllers__backend__.md", "file_size": 14796, "processed_at": "2025-07-29T14:25:50.909765", "collection_name": "test_default_collection", "persist_directory": "data\\chroma_stores\\test_default_collection", "chunk_count": 18, "status": "success"}, "data\\all_docs\\mytask\\06_atlas_university_integration_.md": {"file_name": "06_atlas_university_integration_.md", "file_size": 23461, "processed_at": "2025-07-29T14:25:52.479718", "collection_name": "test_default_collection", "persist_directory": "data\\chroma_stores\\test_default_collection", "chunk_count": 29, "status": "success"}, "data\\all_docs\\mytask\\07_database_models_.md": {"file_name": "07_database_models_.md", "file_size": 17983, "processed_at": "2025-07-29T14:25:53.856839", "collection_name": "test_default_collection", "persist_directory": "data\\chroma_stores\\test_default_collection", "chunk_count": 21, "status": "success"}, "data\\all_docs\\mytask\\08_internationalization__i18n__.md": {"file_name": "08_internationalization__i18n__.md", "file_size": 22685, "processed_at": "2025-07-29T14:25:55.315971", "collection_name": "test_default_collection", "persist_directory": "data\\chroma_stores\\test_default_collection", "chunk_count": 25, "status": "success"}, "data\\all_docs\\mytask\\index.md": {"file_name": "index.md", "file_size": 2265, "processed_at": "2025-07-29T14:25:56.418548", "collection_name": "test_default_collection", "persist_directory": "data\\chroma_stores\\test_default_collection", "chunk_count": 3, "status": "success"}, "data\\all_docs\\subfolder\\nested_document.txt": {"file_name": "nested_document.txt", "file_size": 504, "processed_at": "2025-07-29T14:27:23.518649", "collection_name": "api_test_default", "persist_directory": "data\\chroma_stores\\api_test_default", "chunk_count": 1, "status": "success"}, "data\\academic_docs\\research_paper.txt": {"file_name": "research_paper.txt", "file_size": 982, "processed_at": "2025-07-29T14:27:31.571320", "collection_name": "api_test_academic", "persist_directory": "data\\chroma_stores\\api_test_academic", "chunk_count": 1, "status": "success"}, "data\\student_docs\\course_material.txt": {"file_name": "course_material.txt", "file_size": 726, "processed_at": "2025-07-29T14:33:08.455622", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 1, "status": "success"}, "data\\admin_docs\\system_policy.txt": {"file_name": "system_policy.txt", "file_size": 772, "processed_at": "2025-07-29T14:26:00.769364", "collection_name": "test_admin_collection", "persist_directory": "data\\chroma_stores\\test_admin_collection", "chunk_count": 1, "status": "success"}, "data\\all_docs\\rag\\01_agenticrag_system_.md": {"file_name": "01_agenticrag_system_.md", "processed_at": "2025-07-29T14:27:21.921204", "status": "error", "error": "[WinError 3] Sistem belirtilen yolu bulamıyor: 'data\\\\all_docs\\\\rag\\\\01_agenticrag_system_.md'"}, "data\\academic_docs\\myform\\01_user_authentication___management_.md": {"file_name": "01_user_authentication___management_.md", "processed_at": "2025-07-29T14:27:36.618956", "status": "error", "error": "[WinError 3] Sistem belirtilen yolu bulamıyor: 'data\\\\academic_docs\\\\myform\\\\01_user_authentication___management_.md'"}, "data\\academic_docs\\mytask\\01_user_and_authentication_system_.md": {"file_name": "01_user_and_authentication_system_.md", "processed_at": "2025-07-29T14:27:38.451776", "status": "error", "error": "[WinError 3] Sistem belirtilen yolu bulamıyor: 'data\\\\academic_docs\\\\mytask\\\\01_user_and_authentication_system_.md'"}, "data\\academic_docs\\rag\\01_agenticrag_system_.md": {"file_name": "01_agenticrag_system_.md", "file_size": 13906, "processed_at": "2025-07-29T14:27:39.856737", "collection_name": "api_test_academic", "persist_directory": "data\\chroma_stores\\api_test_academic", "chunk_count": 15, "status": "success"}, "data\\academic_docs\\rag\\02_bot_.md": {"file_name": "02_bot_.md", "processed_at": "2025-07-29T14:27:41.042441", "status": "error", "error": "[WinError 3] Sistem belirtilen yolu bulamıyor: 'data\\\\academic_docs\\\\rag\\\\02_bot_.md'"}, "data\\student_docs\\rag\\01_agenticrag_system_.md": {"file_name": "01_agenticrag_system_.md", "file_size": 13906, "processed_at": "2025-07-29T14:33:13.379684", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 15, "status": "success"}, "data\\student_docs\\rag\\02_bot_.md": {"file_name": "02_bot_.md", "file_size": 12013, "processed_at": "2025-07-29T14:33:14.606431", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 13, "status": "success"}, "data\\student_docs\\rag\\03_query_router_.md": {"file_name": "03_query_router_.md", "file_size": 14207, "processed_at": "2025-07-29T14:33:16.073698", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 17, "status": "success"}, "data\\student_docs\\rag\\04_tool_.md": {"file_name": "04_tool_.md", "file_size": 16782, "processed_at": "2025-07-29T14:33:17.468967", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 19, "status": "success"}, "data\\student_docs\\rag\\05_agent__langgraphagent__.md": {"file_name": "05_agent__langgraphagent__.md", "file_size": 17533, "processed_at": "2025-07-29T14:33:18.687185", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 20, "status": "success"}, "data\\student_docs\\rag\\06_memory_management_.md": {"file_name": "06_memory_management_.md", "file_size": 20431, "processed_at": "2025-07-29T14:33:19.915344", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 24, "status": "success"}, "data\\student_docs\\rag\\07_bot_configuration_.md": {"file_name": "07_bot_configuration_.md", "file_size": 15822, "processed_at": "2025-07-29T14:33:21.216626", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 17, "status": "success"}, "data\\student_docs\\rag\\08_document_processor_.md": {"file_name": "08_document_processor_.md", "file_size": 20687, "processed_at": "2025-07-29T14:33:22.479003", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 24, "status": "success"}, "data\\student_docs\\rag\\index.md": {"file_name": "index.md", "file_size": 1753, "processed_at": "2025-07-29T14:33:23.459210", "collection_name": "student_docs", "persist_directory": "data\\chroma_stores\\student_docs", "chunk_count": 2, "status": "success"}, "data\\atlasiq_docs\\atlasiq_features.txt": {"file_name": "atlasiq_features.txt", "file_size": 1404, "processed_at": "2025-07-29T16:07:18.136564", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 2, "status": "success"}, "data\\atlasiq_docs\\atlasiq_overview.txt": {"file_name": "atlasiq_overview.txt", "file_size": 1040, "processed_at": "2025-07-29T16:07:19.070111", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 1, "status": "success"}, "data\\atlasiq_docs\\atlasiq_usage_guide.txt": {"file_name": "atlasiq_usage_guide.txt", "file_size": 1850, "processed_at": "2025-07-29T16:07:20.212547", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 2, "status": "success"}, "data\\atlasiq_docs\\myform\\01_user_authentication___management_.md": {"file_name": "01_user_authentication___management_.md", "file_size": 40501, "processed_at": "2025-07-29T16:07:36.768058", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 45, "status": "success"}, "data\\atlasiq_docs\\myform\\02_form_structure___management_.md": {"file_name": "02_form_structure___management_.md", "file_size": 27932, "processed_at": "2025-07-29T16:07:38.514563", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 9, "status": "success"}, "data\\atlasiq_docs\\myform\\03_form_responses___analytics_.md": {"file_name": "03_form_responses___analytics_.md", "file_size": 28567, "processed_at": "2025-07-29T16:07:40.542254", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 30, "status": "success"}, "data\\atlasiq_docs\\myform\\04_ai_integration_.md": {"file_name": "04_ai_integration_.md", "file_size": 34848, "processed_at": "2025-07-29T16:07:41.622099", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 6, "status": "success"}, "data\\atlasiq_docs\\myform\\05_next_js_api_routes_.md": {"file_name": "05_next_js_api_routes_.md", "file_size": 20696, "processed_at": "2025-07-29T16:07:43.360777", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 24, "status": "success"}, "data\\atlasiq_docs\\myform\\06_database_connection___models_.md": {"file_name": "06_database_connection___models_.md", "file_size": 20388, "processed_at": "2025-07-29T16:07:45.205357", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 23, "status": "success"}, "data\\atlasiq_docs\\myform\\07_global_middleware_.md": {"file_name": "07_global_middleware_.md", "file_size": 13431, "processed_at": "2025-07-29T16:07:46.927818", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 16, "status": "success"}, "data\\atlasiq_docs\\myform\\08_localization__i18n__.md": {"file_name": "08_localization__i18n__.md", "file_size": 20694, "processed_at": "2025-07-29T16:07:48.649600", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 25, "status": "success"}, "data\\atlasiq_docs\\myform\\index.md": {"file_name": "index.md", "file_size": 1819, "processed_at": "2025-07-29T16:07:50.272873", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 2, "status": "success"}, "data\\atlasiq_docs\\mytask\\01_user_and_authentication_system_.md": {"file_name": "01_user_and_authentication_system_.md", "file_size": 25793, "processed_at": "2025-07-29T16:07:52.110617", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 29, "status": "success"}, "data\\atlasiq_docs\\mytask\\02_task_management_core_.md": {"file_name": "02_task_management_core_.md", "file_size": 20862, "processed_at": "2025-07-29T16:07:53.935982", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 3, "status": "success"}, "data\\atlasiq_docs\\mytask\\03_frontend_state_management__redux_toolkit__.md": {"file_name": "03_frontend_state_management__redux_toolkit__.md", "file_size": 28618, "processed_at": "2025-07-29T16:07:57.860305", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 33, "status": "success"}, "data\\atlasiq_docs\\mytask\\04_api_communication__rtk_query__.md": {"file_name": "04_api_communication__rtk_query__.md", "file_size": 27520, "processed_at": "2025-07-29T16:07:59.232979", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 12, "status": "success"}, "data\\atlasiq_docs\\mytask\\05_api_endpoints_and_controllers__backend__.md": {"file_name": "05_api_endpoints_and_controllers__backend__.md", "file_size": 14796, "processed_at": "2025-07-29T16:08:00.497507", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 18, "status": "success"}, "data\\atlasiq_docs\\mytask\\06_atlas_university_integration_.md": {"file_name": "06_atlas_university_integration_.md", "file_size": 23461, "processed_at": "2025-07-29T16:08:02.165409", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 29, "status": "success"}, "data\\atlasiq_docs\\mytask\\07_database_models_.md": {"file_name": "07_database_models_.md", "file_size": 17983, "processed_at": "2025-07-29T16:08:03.693596", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 21, "status": "success"}, "data\\atlasiq_docs\\mytask\\08_internationalization__i18n__.md": {"file_name": "08_internationalization__i18n__.md", "file_size": 22685, "processed_at": "2025-07-29T16:08:06.154653", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 25, "status": "success"}, "data\\atlasiq_docs\\mytask\\index.md": {"file_name": "index.md", "file_size": 2265, "processed_at": "2025-07-29T16:08:07.035182", "collection_name": "atlas_docs", "persist_directory": "data\\chroma_stores\\atlas_docs", "chunk_count": 3, "status": "success"}, "test_custom_docs\\test_document.txt": {"file_name": "test_document.txt", "file_size": 610, "processed_at": "2025-07-29T16:32:29.779370", "collection_name": "test_custom_directory", "persist_directory": "data\\chroma_stores\\test_custom_directory", "chunk_count": 1, "status": "success"}, "test_user_docs\\my_custom_document.md": {"file_name": "my_custom_document.md", "file_size": 1652, "processed_at": "2025-07-29T16:35:52.295050", "collection_name": "user_custom_docs", "persist_directory": "data\\chroma_stores\\user_custom_docs", "chunk_count": 2, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\01_user_authentication___management_.md": {"file_name": "01_user_authentication___management_.md", "file_size": 40501, "processed_at": "2025-07-29T16:45:32.665312", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 45, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\02_form_structure___management_.md": {"file_name": "02_form_structure___management_.md", "file_size": 27932, "processed_at": "2025-07-29T16:45:34.228229", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 9, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\03_form_responses___analytics_.md": {"file_name": "03_form_responses___analytics_.md", "file_size": 28567, "processed_at": "2025-07-29T16:45:36.348307", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 30, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\04_ai_integration_.md": {"file_name": "04_ai_integration_.md", "file_size": 34848, "processed_at": "2025-07-29T16:45:37.379471", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 6, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\05_next_js_api_routes_.md": {"file_name": "05_next_js_api_routes_.md", "file_size": 20696, "processed_at": "2025-07-29T16:45:38.676143", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 24, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\06_database_connection___models_.md": {"file_name": "06_database_connection___models_.md", "file_size": 20388, "processed_at": "2025-07-29T16:45:41.001677", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 23, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\07_global_middleware_.md": {"file_name": "07_global_middleware_.md", "file_size": 13431, "processed_at": "2025-07-29T16:45:42.903299", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 16, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\08_localization__i18n__.md": {"file_name": "08_localization__i18n__.md", "file_size": 20694, "processed_at": "2025-07-29T16:45:44.352733", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 25, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\pocket-flow\\myform\\myform\\docs\\index.md": {"file_name": "index.md", "file_size": 1819, "processed_at": "2025-07-29T16:45:45.047042", "collection_name": "atlasiq_documents", "persist_directory": "data\\chroma_stores\\atlasiq_documents", "chunk_count": 2, "status": "success"}, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpqe1jdfv1\\test_upload_document.txt": {"file_name": "test_upload_document.txt", "file_size": 310, "processed_at": "2025-07-31T10:59:59.418183", "collection_name": "test_upload_collection", "persist_directory": "data\\chroma_stores\\test_upload_collection", "chunk_count": 1, "status": "success"}, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwn7purnx\\test_upload_doc_1.txt": {"file_name": "test_upload_doc_1.txt", "file_size": 277, "processed_at": "2025-07-31T11:00:05.326671", "collection_name": "test_multi_upload_collection", "persist_directory": "data\\chroma_stores\\test_multi_upload_collection", "chunk_count": 1, "status": "success"}, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwn7purnx\\test_upload_doc_2.txt": {"file_name": "test_upload_doc_2.txt", "file_size": 278, "processed_at": "2025-07-31T11:00:06.855271", "collection_name": "test_multi_upload_collection", "persist_directory": "data\\chroma_stores\\test_multi_upload_collection", "chunk_count": 1, "status": "success"}, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwn7purnx\\test_upload_doc_3.txt": {"file_name": "test_upload_doc_3.txt", "file_size": 278, "processed_at": "2025-07-31T11:00:07.550287", "collection_name": "test_multi_upload_collection", "persist_directory": "data\\chroma_stores\\test_multi_upload_collection", "chunk_count": 1, "status": "success"}}