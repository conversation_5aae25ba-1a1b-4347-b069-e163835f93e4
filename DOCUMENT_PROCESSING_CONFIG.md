# Config Tabanlı Döküman İşleme Sistemi

Bu dokümantasyon, Atlas Q&A RAG projesinde yeni eklenen config tabanlı döküman işleme sistemini açıklar.

## 🎯 Özellikler

### ✅ Yeni Eklenen Özellikler

1. **Config Tabanlı Kaynak Dizinleri**: Dökümanların alınacağı dizinler artık `configs/document_processing.yaml` dosyasından ayarlanabilir
2. **Çoklu Kaynak Dizin Desteği**: Farklı türde dökümanlar için ayrı kaynak dizinleri
3. **Recursive İşleme**: Alt klasörlerdeki dökümanları otomatik işleme
4. **Yeni API Endpoint'leri**: Kaynak dizinlerden döküman işleme için özel endpoint'ler

## 📁 Kaynak Dizin Yapısı

Sistem şu kaynak dizinlerini destekler:

```
data/
├── all_docs/          # Default kaynak dizin (tü<PERSON> dökümanlar)
├── academic_docs/     # Akademik dökümanlar
├── student_docs/      # Öğrenci materyalleri
└── admin_docs/        # Yönetim dökümanları
```

## ⚙️ Konfigürasyon

### Config Dosyası: `configs/document_processing.yaml`

```yaml
# Kaynak dizinleri konfigürasyonu
source_directories:
  # Default kaynak dizin (tüm dökümanlar için)
  default_source_dir: "data/all_docs"
  
  # Farklı türler için özel kaynak dizinleri
  academic_source_dir: "data/academic_docs"
  student_source_dir: "data/student_docs"
  admin_source_dir: "data/admin_docs"
  
  # Alt klasörleri recursive olarak işle
  recursive_processing: true
```

### Desteklenen Dosya Formatları

- PDF (.pdf)
- Microsoft Word (.docx, .doc)
- Metin dosyaları (.txt)
- Markdown (.md)

## 🚀 Kullanım

### 1. Programatik Kullanım

```python
from app.document_processing.document_processor import DocumentProcessor

# DocumentProcessor'ı başlat (config otomatik yüklenir)
processor = DocumentProcessor()

# Default kaynak dizindeki tüm dökümanları işle
result = await processor.process_default_source_directory(
    collection_name="my_collection"
)

# Belirli bir kaynak türündeki dökümanları işle
result = await processor.process_source_directory_by_type(
    source_type="academic",  # academic, student, admin, default
    collection_name="academic_collection"
)

# Kaynak dizinleri listele
source_dirs = processor.get_source_directories()
```

### 2. API Endpoint'leri

#### Kaynak Dizinleri Listele
```http
GET /source-directories
```

#### Default Kaynak Dizini İşle
```http
POST /process-default-source?collection_name=my_collection
```

#### Belirli Kaynak Türü İşle
```http
POST /process-source-directory
Content-Type: application/json

{
    "source_type": "academic",
    "collection_name": "academic_collection",
    "custom_metadata": {
        "category": "research"
    }
}
```

### 3. Swagger UI

API endpoint'lerini test etmek için Swagger UI kullanabilirsiniz:
```
http://localhost:8000/docs
```

## 📊 Test Sonuçları

Sistem test edildi ve şu sonuçlar elde edildi:

- ✅ Config dosyası başarıyla okunuyor
- ✅ Kaynak dizinleri otomatik oluşturuluyor
- ✅ Recursive işleme çalışıyor
- ✅ Tüm API endpoint'leri çalışıyor
- ✅ Farklı kaynak türleri ayrı ayrı işlenebiliyor

## 🔧 Kurulum ve Test

### 1. Test Dosyaları Oluşturma

```bash
# Test için örnek dosyalar oluştur
echo "Test dökümanı" > data/all_docs/test.txt
echo "Akademik makale" > data/academic_docs/paper.txt
echo "Ders materyali" > data/student_docs/lesson.txt
echo "Yönetim politikası" > data/admin_docs/policy.txt
```

### 2. Test Scriptini Çalıştırma

```bash
python test_document_processor.py
```

### 3. API Test

```bash
# API sunucusunu başlat
python -m uvicorn app.main:app --reload --port 8000

# API endpoint'lerini test et
python test_api_endpoints.py
```

## 📝 Örnek Kullanım Senaryoları

### Senaryo 1: Tüm Dökümanları İşleme
```python
# data/all_docs klasöründeki tüm dökümanları işle
result = await processor.process_default_source_directory(
    collection_name="all_documents"
)
```

### Senaryo 2: Sadece Akademik Dökümanları İşleme
```python
# Sadece data/academic_docs klasöründeki dökümanları işle
result = await processor.process_source_directory_by_type(
    source_type="academic",
    collection_name="academic_papers"
)
```

### Senaryo 3: Custom Metadata ile İşleme
```python
result = await processor.process_source_directory_by_type(
    source_type="student",
    collection_name="course_materials",
    custom_metadata={
        "course": "CS101",
        "semester": "2024-1"
    }
)
```

## 🔄 Geriye Uyumluluk

Mevcut `process_directory` metodu hala çalışmaktadır. Yeni özellikler ek olarak eklenmiştir.

## 🎉 Sonuç

Bu güncelleme ile döküman işleme sistemi daha esnek ve yapılandırılabilir hale gelmiştir. Artık farklı türde dökümanları ayrı kaynak dizinlerden işleyebilir ve config dosyası üzerinden kolayca yönetebilirsiniz.
