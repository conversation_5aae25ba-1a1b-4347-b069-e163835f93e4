"""
FastAPI application for the Agentic RAG system.
"""

# import logging
import os
import uuid
import asyncio
from typing import List, Optional

# import time
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, Request, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv
import pyodbc
import pandas as pd

from app.core.agentic_rag import AgenticRAG
from app.agents.langgraph_agent import LangGraphAgent
from app.models.api_models import (
    QueryRequest,
    BotInfo,
    BotsListResponse,
    DocumentProcessRequest,
    DocumentProcessResponse,
    SourceDirectoryProcessRequest,
    SourceDirectoriesResponse,
    CustomPathProcessRequest,
    SqlConnectionRequest,
    SqlConnectionResponse,
    SqlQueryRequest,
    SqlQueryResponse,
    DocumentUploadRequest,
)

# Rate limiting is now handled in the core module
from app.core.logging_config import setup_logging, get_logger, get_api_logger

# from app.core.logging_helpers import (
#     log_api_request,
#     QueryLogger,
#     get_simple_logger_instance,
# )
from app.core.logging_helpers import (
    QueryLogger,
    get_simple_logger_instance,
)

from app.document_processing.document_processor import DocumentProcessor

# Load environment variables
load_dotenv()

# Setup centralized logging
setup_logging(
    log_dir=os.getenv("LOG_DIR", "logs"),
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    enable_console=os.getenv("DEBUG", "False").lower() == "true",
    enable_json_logs=True,
)

# Get loggers
logger = get_logger(__name__)
api_logger = get_api_logger()
simple_logger = get_simple_logger_instance()

# Initialize the AgenticRAG system
logger.info("Initializing AgenticRAG system...")
agentic_rag = AgenticRAG()
logger.info("AgenticRAG system initialized successfully")

# Initialize the DocumentProcessor
logger.info("Initializing DocumentProcessor...")
document_processor = DocumentProcessor()
logger.info("DocumentProcessor initialized successfully")

# Create the FastAPI application
app = FastAPI(
    title="Agentic RAG API",
    description="API for the Agentic RAG system",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Middleware to log all requests
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests."""
    request_id = str(uuid.uuid4())[:8]
    start_time = datetime.now()

    # Log request
    api_logger.info(
        f"[{request_id}] {request.method} {request.url.path}",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else "unknown",
        },
    )

    # Process request
    response = await call_next(request)

    # Calculate duration
    duration = (datetime.now() - start_time).total_seconds()

    # Log response
    api_logger.info(
        f"[{request_id}] Response: {response.status_code} ({duration:.3f}s)",
        extra={
            "request_id": request_id,
            "status_code": response.status_code,
            "duration": duration,
        },
    )

    # Sade log
    simple_logger.api_request(
        request.method, request.url.path, response.status_code, duration
    )

    return response


# Dependency to get the AgenticRAG instance
def get_agentic_rag() -> AgenticRAG:
    return agentic_rag


# Dependency to get the DocumentProcessor instance
def get_document_processor() -> DocumentProcessor:
    return document_processor


@app.get("/", tags=["Health"])
async def root():
    """Root endpoint for health check."""
    return {"status": "ok", "message": "Agentic RAG API is running"}


@app.get("/bots", response_model=BotsListResponse, tags=["Bots"])
async def list_bots(rag: AgenticRAG = Depends(get_agentic_rag)):
    """List all available bots."""
    bot_names = rag.get_bot_names()
    bots = []

    for bot_name in bot_names:
        bot = rag.get_bot(bot_name)
        if bot:
            config = bot["config"]
            bots.append(
                BotInfo(
                    name=config.name,
                    description=config.description,
                    tools=[tool.type for tool in config.tools if tool.enabled],
                    metadata=config.metadata,
                )
            )

    return BotsListResponse(bots=bots)


@app.get("/bots/{bot_name}", response_model=BotInfo, tags=["Bots"])
async def get_bot_info(bot_name: str, rag: AgenticRAG = Depends(get_agentic_rag)):
    """Get information about a specific bot."""
    bot = rag.get_bot(bot_name)
    if not bot:
        raise HTTPException(status_code=404, detail=f"Bot not found: {bot_name}")

    config = bot["config"]
    return BotInfo(
        name=config.name,
        description=config.description,
        tools=[tool.type for tool in config.tools if tool.enabled],
        metadata=config.metadata,
    )


@app.post("/bots/{bot_name}/query", tags=["Queries"])
async def query_bot(
    bot_name: str,
    request: QueryRequest,
    rag: AgenticRAG = Depends(get_agentic_rag),
):
    """Query a specific bot with rate limiting."""
    # Apply bot-specific rate limiting
    try:
        from app.core.rate_limiter import check_bot_rate_limit

        # Get bot configuration
        bot = rag.get_bot(bot_name)
        logger.info(f"Bot found: {bot is not None}")
        if bot:
            config = bot["config"]
            logger.info(f"Bot config type: {type(config)}")
            rate_limit_config = getattr(config, "rate_limit", None)
            logger.info(f"Rate limit config found: {rate_limit_config is not None}")

            # Check rate limits
            check_bot_rate_limit(bot_name, request.session_id, rate_limit_config)
            logger.info(f"Rate limit check passed for bot: {bot_name}")
        else:
            logger.warning(f"Bot not found for rate limiting: {bot_name}")

    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit exceeded)
        raise
    except Exception as e:
        logger.error(f"Error in rate limiting for bot {bot_name}: {str(e)}")
        # Continue without rate limiting if there's an error
    # Initialize query logger
    query_logger_instance = QueryLogger(bot_name=bot_name)

    try:
        # Check for empty query and return default message
        if not request.query or request.query.strip() == "":
            logger.info(
                f"Empty query received for bot: {bot_name}, returning default message"
            )

            # Create default response for empty query
            from app.models.api_models import QueryResponse

            default_response = QueryResponse(
                bot_name=bot_name,
                query="",
                response="Merhaba, nasıl yardımcı olabilirim?",
                tool_responses=[],
                selected_tools=[],
                tool_selection_reasoning="Empty query - default response",
                raw_llm_output={},
                session_id=request.session_id,
                metadata={"default_response": True},
            )

            # Log the default response
            query_logger_instance.log_query_start(
                query_text="", user_id=getattr(request, "user_id", None)
            )
            query_logger_instance.log_response_generation(
                response_length=len(default_response.response), duration=0
            )
            query_logger_instance.log_query_complete(success=True)

            # Sade log - boş sorgu için varsayılan yanıt
            simple_logger.query_received(
                bot_name, "", getattr(request, "user_id", None)
            )
            simple_logger.query_completed(
                bot_name, 0.001, True, len(default_response.response)
            )

            return JSONResponse(
                content=default_response.dict(),
                headers={"Content-Type": "application/json; charset=utf-8"},
            )

        # Log query start
        query_logger_instance.log_query_start(
            query_text=request.query, user_id=getattr(request, "user_id", None)
        )

        # Sade log - sorgu alındı
        simple_logger.query_received(
            bot_name, request.query, getattr(request, "user_id", None)
        )

        # Check if bot exists
        bot = rag.get_bot(bot_name)
        if not bot:
            logger.warning(f"Bot not found: {bot_name}")
            simple_logger.error_occurred("Bot bulunamadı", f"Bot: {bot_name}")
            raise HTTPException(status_code=404, detail=f"Bot not found: {bot_name}")

        # Check if bot is inactive (isactive = 2)
        if hasattr(bot["config"], "isactive") and bot["config"].isactive == 2:
            logger.info(
                f"Bot {bot_name} is inactive (isactive=2), returning default message"
            )

            # Create inactive response
            from app.models.api_models import QueryResponse

            inactive_response = QueryResponse(
                bot_name=bot_name,
                query=request.query,
                response="Şu anda aktif değilim, lütfen daha sonra tekrar deneyin.",
                tool_responses=[],
                selected_tools=[],
                tool_selection_reasoning="Bot inactive",
                raw_llm_output={},
                session_id=request.session_id,
                execution_time=None,
                metadata={"status": "inactive", "isactive": 2},
            )

            # Log inactive bot response
            query_logger_instance.log_response_generation(
                response_length=len(inactive_response.response), duration=0
            )
            query_logger_instance.log_query_complete(success=True)

            # Sade log - inactive bot
            simple_logger.query_received(
                bot_name, request.query, getattr(request, "user_id", None)
            )
            simple_logger.query_completed(
                bot_name, 0.001, True, len(inactive_response.response)
            )

            return JSONResponse(
                content=inactive_response.dict(),
                headers={"Content-Type": "application/json; charset=utf-8"},
            )

        # Log bot found
        logger.info(f"Processing query for bot: {bot_name}")

        # Process query
        start_time = datetime.now()
        response = await rag.process_query(bot_name, request)
        query_duration = (datetime.now() - start_time).total_seconds()

        # Check if bot is in test mode (isactive = 1) and add execution time
        # Also add isactive status to metadata
        bot = rag.get_bot(bot_name)
        if bot and hasattr(bot["config"], "isactive"):
            # Add isactive status to response metadata
            if not response.metadata:
                response.metadata = {}
            response.metadata["isactive"] = bot["config"].isactive

            # Add execution time if bot is in test mode
            if bot["config"].isactive == 1:
                response.execution_time = query_duration

        # Convert to dict and then to JSON with ensure_ascii=False to preserve Turkish characters
        response_dict = response.dict()

        # Ensure all values are JSON serializable
        def ensure_serializable(obj):
            if isinstance(obj, dict):
                return {k: ensure_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [ensure_serializable(item) for item in obj]
            elif hasattr(obj, "keys") and callable(obj.keys):
                # Convert dict-like objects (like ResultProxy.keys()) to lists
                return list(obj)
            else:
                return obj

        # Apply the serialization fix to the response dictionary
        serializable_response = ensure_serializable(response_dict)

        # Log successful response
        response_length = len(str(serializable_response))
        query_logger_instance.log_response_generation(
            response_length=response_length,
            duration=0,  # Duration will be calculated in log_query_complete
        )

        # Log query completion
        query_logger_instance.log_query_complete(success=True)

        # Sade log - sorgu tamamlandı
        simple_logger.query_completed(bot_name, query_duration, True, response_length)

        logger.info(
            f"Query processed successfully for bot: {bot_name}, response length: {response_length}"
        )

        # Return a custom JSONResponse with proper encoding
        return JSONResponse(
            content=serializable_response, media_type="application/json; charset=utf-8"
        )
    except ValueError as e:
        error_msg = f"Validation error for bot {bot_name}: {str(e)}"
        logger.error(error_msg)
        query_logger_instance.log_query_complete(success=False, error=error_msg)
        simple_logger.error_occurred("Doğrulama hatası", str(e), bot_name)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        error_msg = f"Error processing query for bot {bot_name}: {str(e)}"
        logger.error(error_msg)
        import traceback

        full_traceback = traceback.format_exc()
        logger.error(f"Full traceback: {full_traceback}")

        # Log query failure
        query_logger_instance.log_query_complete(success=False, error=error_msg)
        simple_logger.error_occurred("Sorgu işleme hatası", str(e), bot_name)

        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/bots/{bot_name}/clear-memory", tags=["Queries"])
async def clear_memory(
    bot_name: str, session_id: str = None, rag: AgenticRAG = Depends(get_agentic_rag)
):
    """Clear the memory for a specific session."""
    try:
        logger.info(f"Clearing memory for bot: {bot_name}, session: {session_id}")

        bot = rag.get_bot(bot_name)
        if not bot:
            logger.warning(f"Bot not found for memory clear: {bot_name}")
            raise HTTPException(status_code=404, detail=f"Bot not found: {bot_name}")

        agent: LangGraphAgent = bot["agent"]

        if session_id:
            # Clear memory for a specific session
            agent.memory_manager.clear_memory(session_id)
            logger.info(
                f"Memory cleared successfully for bot: {bot_name}, session: {session_id}"
            )
            # Sade log
            simple_logger.memory_cleared(bot_name, session_id)
            return {
                "status": "ok",
                "message": f"Memory cleared for session: {session_id}",
            }
        else:
            # If no session_id is provided, return an error
            logger.warning(
                f"Memory clear attempted without session_id for bot: {bot_name}"
            )
            raise HTTPException(status_code=400, detail="session_id is required")
    except Exception as e:
        error_msg = (
            f"Error clearing memory for bot {bot_name}, session {session_id}: {str(e)}"
        )
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=f"Error clearing memory: {str(e)}")


@app.post(
    "/process-documents", response_model=DocumentProcessResponse, tags=["Documents"]
)
async def process_documents(
    request: DocumentProcessRequest,
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Process documents in a directory and store them in a collection."""
    try:
        logger.info(f"Processing documents in directory: {request.directory_path}")
        logger.info(f"Collection name: {request.collection_name}")

        # Process the directory
        result = await processor.process_directory(
            directory_path=request.directory_path,
            collection_name=request.collection_name,
            recursive=request.recursive,
            custom_metadata=request.custom_metadata,
        )

        if result["success"]:
            message = f"Successfully processed {result['successful_count']} files"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} failed)"

            logger.info(f"Document processing completed: {message}")

            return DocumentProcessResponse(
                success=True,
                message=message,
                directory_path=result["directory_path"],
                collection_name=result["collection_name"],
                total_files=result["total_files"],
                successful_count=result["successful_count"],
                failed_count=result["failed_count"],
                details={"results": result["results"]},
            )
        else:
            logger.error(
                f"Document processing failed: {result.get('error', 'Unknown error')}"
            )
            return DocumentProcessResponse(
                success=False,
                message="Document processing failed",
                error=result.get("error", "Unknown error"),
            )

    except Exception as e:
        error_msg = f"Error processing documents: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Processing traceback: {traceback.format_exc()}")

        return DocumentProcessResponse(
            success=False, message="Document processing failed", error=error_msg
        )


@app.post(
    "/process-source-directory",
    response_model=DocumentProcessResponse,
    tags=["Documents"],
)
async def process_source_directory(
    request: SourceDirectoryProcessRequest,
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Process documents from a configured source directory."""
    try:
        logger.info(f"Processing documents from source type: {request.source_type}")
        logger.info(f"Collection name: {request.collection_name}")

        # Process the source directory
        result = await processor.process_source_directory_by_type(
            source_type=request.source_type,
            collection_name=request.collection_name,
            custom_metadata=request.custom_metadata,
        )

        if result["success"]:
            message = f"Successfully processed {result['successful_count']} files from {request.source_type} source"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} failed)"

            logger.info(f"Source directory processing completed: {message}")

            return DocumentProcessResponse(
                success=True,
                message=message,
                directory_path=result["directory_path"],
                collection_name=result["collection_name"],
                total_files=result["total_files"],
                successful_count=result["successful_count"],
                failed_count=result["failed_count"],
                details={"results": result["results"]},
            )
        else:
            logger.error(
                f"Source directory processing failed: {result.get('error', 'Unknown error')}"
            )
            return DocumentProcessResponse(
                success=False,
                message="Source directory processing failed",
                error=result.get("error", "Unknown error"),
            )

    except Exception as e:
        error_msg = f"Error processing source directory: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Processing traceback: {traceback.format_exc()}")

        return DocumentProcessResponse(
            success=False, message="Source directory processing failed", error=error_msg
        )


@app.post(
    "/process-custom-path", response_model=DocumentProcessResponse, tags=["Documents"]
)
async def process_custom_path(
    request: CustomPathProcessRequest,
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Process documents from a custom user-specified file or directory path."""
    try:
        logger.info(f"Processing custom path: {request.file_path}")
        logger.info(f"Collection name: {request.collection_name}")

        from pathlib import Path

        file_path = Path(request.file_path)

        # Check if path exists
        if not file_path.exists():
            error_msg = f"Path does not exist: {request.file_path}"
            logger.error(error_msg)
            return DocumentProcessResponse(
                success=False, message="Path not found", error=error_msg
            )

        # Check if it's a file or directory
        if file_path.is_file():
            # Process single file
            logger.info(f"Processing single file: {file_path}")
            result = await processor.process_document(
                file_path=str(file_path),
                collection_name=request.collection_name,
                custom_metadata=request.custom_metadata,
            )

            if result["success"]:
                message = f"Successfully processed file: {file_path.name}"
                logger.info(message)
                return DocumentProcessResponse(
                    success=True,
                    message=message,
                    directory_path=str(file_path.parent),
                    collection_name=result["collection_name"],
                    total_files=1,
                    successful_count=1,
                    failed_count=0,
                    details={"file_info": result},
                )
            else:
                logger.error(
                    f"File processing failed: {result.get('error', 'Unknown error')}"
                )
                return DocumentProcessResponse(
                    success=False,
                    message="File processing failed",
                    error=result.get("error", "Unknown error"),
                )

        elif file_path.is_dir():
            # Process directory
            logger.info(f"Processing directory: {file_path}")
            result = await processor.process_directory(
                directory_path=str(file_path),
                collection_name=request.collection_name,
                recursive=request.recursive,
                custom_metadata=request.custom_metadata,
            )

            if result["success"]:
                message = f"Successfully processed {result['successful_count']} files from directory: {file_path.name}"
                logger.info(message)
                return DocumentProcessResponse(
                    success=True,
                    message=message,
                    directory_path=result["directory_path"],
                    collection_name=result["collection_name"],
                    total_files=result["total_files"],
                    successful_count=result["successful_count"],
                    failed_count=result["failed_count"],
                    details={"results": result["results"]},
                )
            else:
                logger.error(
                    f"Directory processing failed: {result.get('error', 'Unknown error')}"
                )
                return DocumentProcessResponse(
                    success=False,
                    message="Directory processing failed",
                    error=result.get("error", "Unknown error"),
                )
        else:
            error_msg = f"Path is neither a file nor a directory: {request.file_path}"
            logger.error(error_msg)
            return DocumentProcessResponse(
                success=False, message="Invalid path type", error=error_msg
            )

    except Exception as e:
        error_msg = f"Error processing custom path: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Processing traceback: {traceback.format_exc()}")

        return DocumentProcessResponse(
            success=False, message="Custom path processing failed", error=error_msg
        )


@app.post(
    "/process-default-source",
    response_model=DocumentProcessResponse,
    tags=["Documents"],
)
async def process_default_source(
    collection_name: str,
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Process documents from the default source directory."""
    try:
        logger.info(f"Processing documents from default source directory")
        logger.info(f"Collection name: {collection_name}")

        # Process the default source directory
        result = await processor.process_default_source_directory(
            collection_name=collection_name,
        )

        if result["success"]:
            message = f"Successfully processed {result['successful_count']} files from default source"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} failed)"

            logger.info(f"Default source processing completed: {message}")

            return DocumentProcessResponse(
                success=True,
                message=message,
                directory_path=result["directory_path"],
                collection_name=result["collection_name"],
                total_files=result["total_files"],
                successful_count=result["successful_count"],
                failed_count=result["failed_count"],
                details={"results": result["results"]},
            )
        else:
            logger.error(
                f"Default source processing failed: {result.get('error', 'Unknown error')}"
            )
            return DocumentProcessResponse(
                success=False,
                message="Default source processing failed",
                error=result.get("error", "Unknown error"),
            )

    except Exception as e:
        error_msg = f"Error processing default source: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Processing traceback: {traceback.format_exc()}")

        return DocumentProcessResponse(
            success=False, message="Default source processing failed", error=error_msg
        )


@app.get(
    "/source-directories", response_model=SourceDirectoriesResponse, tags=["Documents"]
)
async def get_source_directories(
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Get all configured source directories."""
    try:
        source_dirs = processor.get_source_directories()
        return SourceDirectoriesResponse(source_directories=source_dirs)
    except Exception as e:
        error_msg = f"Error getting source directories: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@app.post("/reload", tags=["Admin"])
async def reload_bots(rag: AgenticRAG = Depends(get_agentic_rag)):
    """Reload all bot configurations."""
    try:
        logger.info("Starting bot configuration reload...")

        # Reload configurations
        rag.config_loader.reload_configs()
        logger.info("Bot configurations reloaded successfully")

        # Reload bots
        rag._load_bots()
        logger.info("Bots reloaded successfully")

        # Sade log
        bot_count = len(rag.get_bot_names())
        simple_logger.config_reloaded(True, bot_count)

        return {"status": "ok", "message": "Bots reloaded successfully"}
    except Exception as e:
        error_msg = f"Error reloading bots: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Reload traceback: {traceback.format_exc()}")
        simple_logger.config_reloaded(False)
        raise HTTPException(status_code=500, detail=f"Error reloading bots: {str(e)}")


@app.post(
    "/test-sql-connection", response_model=SqlConnectionResponse, tags=["Database"]
)
async def test_sql_connection(request: SqlConnectionRequest):
    """Test SQL database connection."""
    try:
        logger.info("Testing SQL database connection...")

        # Test connection
        conn = await asyncio.to_thread(pyodbc.connect, request.connection_string)
        await asyncio.to_thread(conn.close)

        logger.info("✅ SQL connection successful")
        # Use a custom log message for SQL connection success
        simple_logger.logger.info("🔗 SQL BAĞLANTI TESTİ | ✅ BAŞARILI")

        return SqlConnectionResponse(success=True, message="✅ Bağlantı başarılı.")

    except Exception as e:
        error_msg = f"❌ Bağlantı başarısız: {str(e)}"
        logger.error(f"SQL connection test failed: {str(e)}")
        simple_logger.error_occurred("SQL bağlantı hatası", str(e))

        return SqlConnectionResponse(success=False, message=error_msg, error=str(e))


@app.post("/test-sql-query", response_model=SqlQueryResponse, tags=["Database"])
async def test_sql_query(request: SqlQueryRequest):
    """Test SQL database query and list tables with sample data."""
    try:
        logger.info("Testing SQL database query...")

        # Test connection first
        conn = await asyncio.to_thread(pyodbc.connect, request.connection_string)
        logger.info("✅ Bağlantı başarılı.")

        # Get list of tables
        cursor = conn.cursor()
        await asyncio.to_thread(
            cursor.execute,
            """
            SELECT TABLE_SCHEMA, TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
        """,
        )
        tables = await asyncio.to_thread(cursor.fetchall)

        all_dataframes = {}
        table_names = []

        # Get first 5 rows from each table
        for schema, table in tables:
            table_name = f"{schema}.{table}"
            table_names.append(table_name)
            query = f"SELECT TOP 5 * FROM [{schema}].[{table}]"
            try:
                df = await asyncio.to_thread(pd.read_sql, query, conn)
                all_dataframes[table_name] = df
                logger.info(f"✅ {table_name} yüklendi ({len(df)} satır).")
            except Exception as e:
                logger.warning(f"❌ {table_name} yüklenemedi: {e}")

        await asyncio.to_thread(conn.close)

        # Prepare sample data from first table
        sample_data = None
        if all_dataframes:
            first_table = list(all_dataframes.keys())[0]
            first_df = all_dataframes[first_table]
            sample_data = {
                "table_name": first_table,
                "row_count": len(first_df),
                "columns": list(first_df.columns),
                "sample_rows": first_df.to_dict("records"),
            }

        success_message = f"✅ Sorgu başarılı. {len(table_names)} tablo yüklendi."
        logger.info(success_message)
        # Use a custom log message for SQL query success
        simple_logger.logger.info(
            f"📊 SQL SORGU TESTİ | ✅ BAŞARILI | {len(table_names)} tablo yüklendi"
        )

        return SqlQueryResponse(
            success=True,
            message=success_message,
            tables=table_names,
            sample_data=sample_data,
        )

    except Exception as e:
        error_msg = f"❌ Sorgu başarısız: {str(e)}"
        logger.error(f"SQL query test failed: {str(e)}")
        simple_logger.error_occurred("SQL sorgu hatası", str(e))

        return SqlQueryResponse(success=False, message=error_msg, error=str(e))


@app.post(
    "/process-documents-upload",
    response_model=DocumentProcessResponse,
    tags=["Documents"],
)
async def process_documents_upload(
    files: List[UploadFile] = File(..., description="Files to upload and process"),
    collection_name: str = Form(
        ..., description="Name of the collection to store documents"
    ),
    custom_metadata: Optional[str] = Form(
        None, description="Additional metadata as JSON string"
    ),
    overwrite_collection: bool = Form(
        False, description="Whether to overwrite existing collection"
    ),
    recursive: bool = Form(
        True, description="Whether to process subdirectories recursively"
    ),
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Upload and process documents from external sources. Supports both individual files and directory structures."""
    import tempfile
    import shutil
    import json
    import zipfile
    from pathlib import Path

    try:
        logger.info(f"Processing {len(files)} uploaded files/directories")
        logger.info(f"Collection name: {collection_name}")
        logger.info(f"Recursive processing: {recursive}")

        # Parse custom metadata if provided
        parsed_metadata = None
        if custom_metadata:
            try:
                parsed_metadata = json.loads(custom_metadata)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in custom_metadata: {str(e)}")
                return DocumentProcessResponse(
                    success=False,
                    message="Invalid JSON format in custom_metadata",
                    error=str(e),
                )

        # Create temporary directory for uploaded files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            upload_root = temp_path / "uploads"
            upload_root.mkdir(exist_ok=True)

            # Process uploaded files and extract directories if needed
            for file in files:
                if not file.filename:
                    continue

                # Create safe filename
                safe_filename = file.filename.replace(" ", "_").replace("..", "")
                file_path = upload_root / safe_filename

                # Save file
                content = await file.read()
                with open(file_path, "wb") as buffer:
                    buffer.write(content)

                logger.info(
                    f"Saved uploaded file: {safe_filename} ({len(content)} bytes)"
                )

                # Check if it's a zip file (directory upload)
                if safe_filename.lower().endswith(".zip"):
                    logger.info(f"Extracting directory from zip: {safe_filename}")
                    try:
                        # Extract zip file
                        extract_path = upload_root / safe_filename.replace(
                            ".zip", "_extracted"
                        )
                        extract_path.mkdir(exist_ok=True)

                        with zipfile.ZipFile(file_path, "r") as zip_ref:
                            zip_ref.extractall(extract_path)

                        # Remove the zip file after extraction
                        file_path.unlink()
                        logger.info(f"Successfully extracted directory: {extract_path}")

                    except zipfile.BadZipFile:
                        logger.warning(
                            f"File {safe_filename} is not a valid zip file, treating as regular file"
                        )
                        # Rename back to original extension if it's not actually a zip
                        new_path = upload_root / safe_filename.replace(".zip", "")
                        file_path.rename(new_path)
                    except Exception as e:
                        logger.error(
                            f"Error extracting zip file {safe_filename}: {str(e)}"
                        )
                        continue

            # Now process the upload directory using the existing directory processing logic
            logger.info(f"Processing upload directory: {upload_root}")

            # Use the existing process_directory method which handles recursive processing
            result = await processor.process_directory(
                directory_path=str(upload_root),
                collection_name=collection_name,
                custom_metadata=parsed_metadata,
                recursive=recursive,
            )

            # Update the result to indicate it came from upload
            if result.get("details"):
                result["details"]["upload_source"] = "external"
                result["details"]["upload_method"] = "directory_and_files"
            else:
                result["details"] = {
                    "upload_source": "external",
                    "upload_method": "directory_and_files",
                }

            # Update directory path to indicate upload source
            result["directory_path"] = "uploaded_files"

            logger.info(
                f"Upload processing completed: {result.get('message', 'Processing finished')}"
            )

            return DocumentProcessResponse(**result)

    except Exception as e:
        error_msg = f"Error processing uploaded documents: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Upload processing traceback: {traceback.format_exc()}")

        return DocumentProcessResponse(
            success=False, message="Document upload processing failed", error=error_msg
        )


def start():
    """Start the FastAPI application with Uvicorn."""
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "False").lower() == "true"

    logger.info("Starting Agentic RAG API server...")
    logger.info(f"Host: {host}")
    logger.info(f"Port: {port}")
    logger.info(f"Debug mode: {debug}")
    logger.info(f"Log directory: {os.getenv('LOG_DIR', 'logs')}")

    # Sade log - sistem başlatılıyor
    simple_logger.system_start(host, port, debug)

    try:
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=debug,
        )
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
        raise


if __name__ == "__main__":
    start()
